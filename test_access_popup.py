import streamlit as st
from datetime import datetime

def show_access_granted_popup(name, confidence, reason, mode, time):
    """Show a visually appealing popup for access granted"""
    # Set popup state to show
    st.session_state.show_access_granted_popup = True
    st.session_state.access_granted_data = {
        'name': name,
        'confidence': confidence,
        'reason': reason,
        'mode': mode,
        'time': time
    }
    
    # Show the popup modal
    if st.session_state.show_access_granted_popup:
        @st.dialog("✅ Access Granted!")
        def access_granted_popup():
            data = st.session_state.access_granted_data
            
            # Custom CSS for the popup
            st.markdown("""
            <style>
            /* Access Granted Popup Styling */
            .stDialog > div > div {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                border-radius: 20px !important;
                border: none !important;
                box-shadow: 0 20px 60px rgba(16, 185, 129, 0.4) !important;
                animation: popupSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
                backdrop-filter: blur(20px) !important;
            }
            
            .stDialog h1 {
                color: white !important;
                text-align: center !important;
                font-size: 2rem !important;
                margin-bottom: 1.5rem !important;
                text-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
            }
            
            @keyframes popupSlideIn {
                0% {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            
            @keyframes checkmarkBounce {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.2); }
            }
            
            .popup-icon {
                font-size: 4rem;
                text-align: center;
                margin-bottom: 1rem;
                animation: checkmarkBounce 1s ease-in-out infinite;
            }
            
            .popup-message {
                color: white;
                text-align: center;
                font-size: 1.2rem;
                margin-bottom: 2rem;
                line-height: 1.6;
                text-shadow: 0 1px 5px rgba(0,0,0,0.3);
            }
            
            .popup-details {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 1rem;
                margin-bottom: 1.5rem;
                backdrop-filter: blur(10px);
            }
            
            .detail-item {
                color: white;
                margin-bottom: 0.5rem;
                font-size: 1rem;
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Popup content
            st.markdown('<div class="popup-icon">✅</div>', unsafe_allow_html=True)
            
            st.markdown(f"""
            <div class="popup-message">
                <strong>Welcome, {data['name']}!</strong><br>
                Access has been granted successfully.
            </div>
            """, unsafe_allow_html=True)
            
            # Details section
            st.markdown(f"""
            <div class="popup-details">
                <div class="detail-item"><strong>👤 Name:</strong> {data['name']}</div>
                <div class="detail-item"><strong>🎯 Confidence:</strong> {data['confidence']:.0f}%</div>
                <div class="detail-item"><strong>📋 Reason:</strong> {data['reason']}</div>
                <div class="detail-item"><strong>🔧 Mode:</strong> {data['mode']}</div>
                <div class="detail-item"><strong>⏰ Time:</strong> {data['time']}</div>
            </div>
            """, unsafe_allow_html=True)
            
            # Close button
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                if st.button("✓ Acknowledge", 
                           type="primary", 
                           use_container_width=True,
                           key="access_granted_ok_btn"):
                    st.session_state.show_access_granted_popup = False
                    st.rerun()
        
        access_granted_popup()

def show_access_denied_popup(name, confidence, reason, time):
    """Show a visually appealing popup for access denied"""
    # Set popup state to show
    st.session_state.show_access_denied_popup = True
    st.session_state.access_denied_data = {
        'name': name,
        'confidence': confidence,
        'reason': reason,
        'time': time
    }
    
    # Show the popup modal
    if st.session_state.show_access_denied_popup:
        @st.dialog("❌ Access Denied!")
        def access_denied_popup():
            data = st.session_state.access_denied_data
            
            # Custom CSS for the popup
            st.markdown("""
            <style>
            /* Access Denied Popup Styling */
            .stDialog > div > div {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
                border-radius: 20px !important;
                border: none !important;
                box-shadow: 0 20px 60px rgba(220, 38, 38, 0.4) !important;
                animation: popupSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
                backdrop-filter: blur(20px) !important;
            }
            
            .stDialog h1 {
                color: white !important;
                text-align: center !important;
                font-size: 2rem !important;
                margin-bottom: 1.5rem !important;
                text-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
            }
            
            @keyframes popupSlideIn {
                0% {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            
            @keyframes warningPulse {
                0%, 100% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.1); opacity: 0.8; }
            }
            
            .popup-icon {
                font-size: 4rem;
                text-align: center;
                margin-bottom: 1rem;
                animation: warningPulse 1.5s ease-in-out infinite;
            }
            
            .popup-message {
                color: white;
                text-align: center;
                font-size: 1.2rem;
                margin-bottom: 2rem;
                line-height: 1.6;
                text-shadow: 0 1px 5px rgba(0,0,0,0.3);
            }
            
            .popup-details {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 1rem;
                margin-bottom: 1.5rem;
                backdrop-filter: blur(10px);
            }
            
            .detail-item {
                color: white;
                margin-bottom: 0.5rem;
                font-size: 1rem;
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Popup content
            st.markdown('<div class="popup-icon">❌</div>', unsafe_allow_html=True)
            
            st.markdown(f"""
            <div class="popup-message">
                <strong>Access Denied</strong><br>
                {data['name']}, please ensure you meet all requirements.
            </div>
            """, unsafe_allow_html=True)
            
            # Details section
            st.markdown(f"""
            <div class="popup-details">
                <div class="detail-item"><strong>👤 Name:</strong> {data['name']}</div>
                <div class="detail-item"><strong>🎯 Confidence:</strong> {data['confidence']:.0f}%</div>
                <div class="detail-item"><strong>❗ Reason:</strong> {data['reason']}</div>
                <div class="detail-item"><strong>⏰ Time:</strong> {data['time']}</div>
            </div>
            """, unsafe_allow_html=True)
            
            # Close button
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                if st.button("✓ Understood", 
                           type="primary", 
                           use_container_width=True,
                           key="access_denied_ok_btn"):
                    st.session_state.show_access_denied_popup = False
                    st.rerun()
        
        access_denied_popup()

# Main app
st.title("🧪 Access Notification Popup Test")

# Initialize session state
if 'show_access_granted_popup' not in st.session_state:
    st.session_state.show_access_granted_popup = False
if 'show_access_denied_popup' not in st.session_state:
    st.session_state.show_access_denied_popup = False

st.markdown("### Test the new popup notifications for access granted/denied messages")

col1, col2 = st.columns(2)

with col1:
    if st.button("✅ Test Access Granted", type="primary", use_container_width=True):
        show_access_granted_popup(
            name="John Doe",
            confidence=95.5,
            reason="Face recognized with full PPE compliance",
            mode="Enhanced PPE Mode",
            time=datetime.now().strftime('%H:%M:%S')
        )

with col2:
    if st.button("❌ Test Access Denied", type="secondary", use_container_width=True):
        show_access_denied_popup(
            name="Jane Smith",
            confidence=87.2,
            reason="Missing PPE: Hardhat, Safety Vest",
            time=datetime.now().strftime('%H:%M:%S')
        )

st.markdown("---")
st.markdown("**Instructions:**")
st.markdown("1. Click either button above to test the popup notifications")
st.markdown("2. The popups should appear as modal dialogs with beautiful styling")
st.markdown("3. These replace the simple banner notifications that previously appeared only in the terminal")
